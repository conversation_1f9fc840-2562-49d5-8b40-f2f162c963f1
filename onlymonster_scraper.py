"""
OnlyMonster.ai Tracking Links Scraper
Automates login and data extraction from tracking links page
"""
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from database import TrackingDatabase
from analytics import TrackingAnalytics
from slack_webhook import SlackWebhookNotifier
from config import <PERSON>MAIL, PASSWORD, LOGIN_URL, TRACKING_LINKS_URL, HEADLESS, IMPL<PERSON>IT_WAIT, PAGE_LOAD_TIMEOUT, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID


class OnlyMonsterScraper:
    def __init__(self):
        self.driver = None
        self.db = TrackingDatabase()
        self.setup_driver()
    
    def setup_driver(self):
        """Initialize Chrome WebDriver with appropriate options"""
        chrome_options = Options()

        # Always run headless in server environment
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")

        # More realistic browser headers to bypass Cloudflare
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # Options to help with JavaScript execution and bypass detection
        chrome_options.add_argument("--enable-javascript")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Additional options to bypass Cloudflare and other protections
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-client-side-phishing-detection")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--disable-hang-monitor")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-prompt-on-repost")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-translate")
        chrome_options.add_argument("--disable-web-resources")
        chrome_options.add_argument("--enable-automation")
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--ignore-ssl-errors")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer")

        # Set additional preferences
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "geolocation": 2,
                "media_stream": 2,
            },
            "profile.managed_default_content_settings": {
                "images": 1
            }
        }
        chrome_options.add_experimental_option("prefs", prefs)

        try:
            # Try to use system chromedriver first
            service = Service("/usr/bin/chromedriver")
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e1:
            print(f"System chromedriver failed: {e1}")
            # Fallback to ChromeDriverManager
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e2:
                print(f"ChromeDriverManager also failed: {e2}")
                raise Exception(f"Failed to initialize Chrome WebDriver. System: {e1}, Manager: {e2}")

        self.driver.implicitly_wait(IMPLICIT_WAIT)
        self.driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)

        # Execute script to make browser appear more human-like
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        })
    
    def login(self):
        """Login to OnlyMonster.ai with Clerk.js authentication"""
        print("Navigating to OnlyMonster.ai sign-in page...")
        self.driver.get(LOGIN_URL)

        # Wait for initial page load
        time.sleep(5)

        try:
            # Wait for Clerk.js to load and mount the sign-in form
            print("Waiting for Clerk.js authentication form to load...")

            # First wait for the sign-in-block div to be present
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.ID, "sign-in-block"))
            )

            # Try to bypass Cloudflare by executing some JavaScript
            print("Attempting to bypass Cloudflare protection...")
            self.driver.execute_script("""
                // Remove webdriver property
                delete navigator.__proto__.webdriver;

                // Override navigator properties
                Object.defineProperty(navigator, 'plugins', {
                    get: function() { return [1, 2, 3, 4, 5]; }
                });

                Object.defineProperty(navigator, 'languages', {
                    get: function() { return ['en-US', 'en']; }
                });

                // Trigger a user interaction
                document.dispatchEvent(new Event('click'));
                document.dispatchEvent(new Event('mousemove'));
            """)

            time.sleep(3)

            # Wait for Clerk.js script to load and execute
            print("Waiting for Clerk.js script to execute...")
            max_attempts = 30
            clerk_loaded = False

            for attempt in range(max_attempts):
                try:
                    clerk_loaded = self.driver.execute_script("return window.Clerk !== undefined")
                    if clerk_loaded:
                        print(f"Clerk.js loaded successfully after {attempt + 1} attempts")
                        break
                except:
                    pass
                time.sleep(1)
                print(f"Attempt {attempt + 1}/{max_attempts}: Waiting for Clerk.js...")

            if not clerk_loaded:
                print("Clerk.js failed to load, trying alternative approach...")
                # Try to manually trigger the script loading
                self.driver.execute_script("""
                    if (!window.Clerk) {
                        const script = document.createElement('script');
                        script.setAttribute('data-clerk-publishable-key', 'pk_live_Y2xlcmsub25seW1vbnN0ZXIuYWkk');
                        script.async = true;
                        script.src = 'https://accounts.onlymonster.ai/npm/@clerk/clerk-js@latest/dist/clerk.browser.js';
                        document.head.appendChild(script);
                    }
                """)
                time.sleep(5)

            # Wait for the sign-in form to be mounted
            print("Waiting for sign-in form to be mounted...")
            form_mounted = False
            for attempt in range(30):
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, "#sign-in-block *")
                    if len(elements) > 0:
                        print(f"Sign-in form mounted successfully with {len(elements)} elements")
                        form_mounted = True
                        break
                except:
                    pass
                time.sleep(1)
                print(f"Attempt {attempt + 1}/30: Waiting for form to mount...")

            if not form_mounted:
                print("Form failed to mount, checking page content...")
                page_content = self.driver.execute_script("return document.getElementById('sign-in-block').innerHTML")
                print(f"Sign-in block content: {page_content[:200]}...")

                # Try to force form mounting
                self.driver.execute_script("""
                    if (window.Clerk && window.Clerk.mountSignIn) {
                        window.Clerk.mountSignIn(document.querySelector('#sign-in-block'), {
                            redirectUrl: 'https://onlymonster.ai/panel',
                            afterSignInUrl: 'https://onlymonster.ai/panel',
                            forceRedirectUrl: 'https://onlymonster.ai/panel',
                            signUpUrl: '/auth/signup'
                        });
                    }
                """)
                time.sleep(3)

            # Additional wait for form to be fully rendered
            time.sleep(3)

            # Wait for the email input field to appear within the Clerk form
            print("Step 1: Waiting for email input field...")
            email_input = WebDriverWait(self.driver, 30).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "#sign-in-block input[type='email'], #sign-in-block input[name='emailAddress'], #sign-in-block input[name='identifier'], #sign-in-block input[autocomplete='username']"))
            )

            # Clear and enter email
            email_input.clear()
            email_input.send_keys(EMAIL)
            print("Email entered successfully")

            # Find and click the Continue/Next button within the Clerk form
            print("Looking for Continue button...")
            next_button = None

            # Try Clerk-specific selectors first
            clerk_selectors = [
                "#sign-in-block button[data-localization-key='formButtonPrimary']",
                "#sign-in-block .cl-formButtonPrimary",
                "#sign-in-block button[type='submit']"
            ]

            for selector in clerk_selectors:
                try:
                    next_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"Found Continue button with selector: {selector}")
                    break
                except:
                    continue

            if not next_button:
                # Fallback: look for any clickable button in the sign-in block
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, "#sign-in-block button")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower().strip()
                            if button_text in ['continue', 'next', 'submit'] or button_text == '':
                                next_button = button
                                print(f"Found fallback button with text: '{button.text}'")
                                break
                except:
                    pass

            if not next_button:
                raise Exception("Could not find Continue/Next button in Clerk form")

            next_button.click()
            print("Continue button clicked")

            # Step 2: Wait for password field and enter password
            print("Step 2: Waiting for password field...")
            time.sleep(3)  # Give the page time to load the password field

            password_input = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "#sign-in-block input[type='password'], #sign-in-block input[name='password']"))
            )
            password_input.clear()
            password_input.send_keys(PASSWORD)
            print("Password entered successfully")

            # Find and click the Continue button after password entry within Clerk form
            print("Looking for final Continue button...")
            login_button = None

            # Try Clerk-specific selectors for the final Continue button
            continue_selectors = [
                "#sign-in-block .cl-formButtonPrimary",  # Specific class from the HTML
                "#sign-in-block button[data-localization-key='formButtonPrimary']",  # Specific attribute
                "#sign-in-block button[type='submit']",
                "#sign-in-block input[type='submit']"
            ]

            for selector in continue_selectors:
                try:
                    login_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"Found Continue button with selector: {selector}")
                    break
                except:
                    continue

            if not login_button:
                # Fallback: look for any button with "continue" text within the Clerk form
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, "#sign-in-block button")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower().strip()
                            if button_text in ['continue', 'login', 'sign in', 'submit'] or button_text == '':
                                login_button = button
                                print(f"Found button with text: '{button.text}'")
                                break
                except:
                    pass

            if not login_button:
                raise Exception("Could not find Continue/Login button in Clerk form")

            login_button.click()
            print("Continue button clicked")

            # Wait for successful login (check for dashboard or redirect)
            print("Waiting for redirect to panel...")
            try:
                WebDriverWait(self.driver, 20).until(
                    lambda driver: "panel" in driver.current_url.lower() or "dashboard" in driver.current_url.lower()
                )
                print("Login successful!")
            except TimeoutException:
                print(f"Redirect timeout. Current URL: {self.driver.current_url}")
                # Check if we're still on auth page or if there's an error
                if "auth" in self.driver.current_url:
                    print("Still on auth page, checking for errors...")
                    # Look for error messages
                    try:
                        error_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='alert'], .error, .cl-formFieldError")
                        if error_elements:
                            for error in error_elements:
                                if error.text.strip():
                                    print(f"Error found: {error.text}")
                    except:
                        pass
                raise

        except TimeoutException:
            print("Login failed - timeout waiting for elements")
            # Save page source for debugging
            with open("login_debug.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print("Page source saved to login_debug.html for debugging")
            raise
        except NoSuchElementException as e:
            print(f"Login failed - element not found: {e}")
            # Save page source for debugging
            with open("login_debug.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print("Page source saved to login_debug.html for debugging")
            raise
    
    def navigate_to_tracking_links(self):
        """Navigate to the tracking links page"""
        print("Navigating to tracking links page...")
        self.driver.get(TRACKING_LINKS_URL)
        
        # Wait for page to load
        WebDriverWait(self.driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(3)  # Additional wait for dynamic content
        print("Tracking links page loaded")
    
    def take_debug_screenshot(self, filename):
        """Take a screenshot for debugging purposes"""
        try:
            self.driver.save_screenshot(filename)
            print(f"Debug screenshot saved: {filename}")
        except Exception as e:
            print(f"Could not save screenshot: {e}")

    def load_all_tracking_links(self):
        """Click 'Load More' button until all tracking links are loaded"""
        print("Loading all tracking links...")

        # Take initial screenshot
        self.take_debug_screenshot("before_load_more.png")

        max_attempts = 10  # Prevent infinite loops
        attempts = 0

        while attempts < max_attempts:
            try:
                # Look for "Load More" button with various possible selectors
                load_more_selectors = [
                    "button:contains('Load More')",
                    "button:contains('load more')",
                    "button:contains('Show More')",
                    "button:contains('show more')",
                    ".load-more",
                    ".show-more",
                    "[data-action='load-more']",
                    "button[onclick*='load']",
                    "button[onclick*='more']"
                ]

                load_more_button = None

                # Try to find Load More button using text content
                all_clickable_elements = self.driver.find_elements(By.XPATH, "//button | //a | //div[@role='button'] | //span[@role='button']")
                for element in all_clickable_elements:
                    try:
                        element_text = element.text.lower().strip()
                        # Check for various Load More text patterns
                        load_more_patterns = [
                            'load more', 'show more', 'load all', 'see more',
                            'view more', 'more results', 'show all', 'load additional',
                            'expand', 'see all'
                        ]

                        if any(phrase in element_text for phrase in load_more_patterns):
                            if element.is_displayed() and element.is_enabled():
                                load_more_button = element
                                print(f"Found Load More element with text: '{element.text}' (tag: {element.tag_name})")
                                break
                    except:
                        continue

                # If not found by text, try CSS selectors
                if not load_more_button:
                    for selector in load_more_selectors:
                        try:
                            potential_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for btn in potential_buttons:
                                if btn.is_displayed() and btn.is_enabled():
                                    load_more_button = btn
                                    print(f"Found Load More button with selector: {selector}")
                                    break
                            if load_more_button:
                                break
                        except:
                            continue

                if load_more_button:
                    # Scroll to the button to ensure it's visible
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", load_more_button)
                    time.sleep(1)

                    # Click the Load More button
                    try:
                        load_more_button.click()
                        print(f"Clicked Load More button (attempt {attempts + 1})")
                        time.sleep(3)  # Wait for new content to load
                        attempts += 1
                    except Exception as e:
                        print(f"Error clicking Load More button: {e}")
                        break
                else:
                    print("No more Load More button found - all content loaded")
                    break

            except Exception as e:
                print(f"Error during Load More process: {e}")
                break

        print(f"Completed Load More process after {attempts} attempts")

        # Take final screenshot
        self.take_debug_screenshot("after_load_more.png")

    def extract_tracking_data(self):
        """Extract tracking links data from the page"""
        print("Extracting tracking data...")
        tracking_data = []

        try:
            # First, load all tracking links by clicking Load More buttons
            self.load_all_tracking_links()

            # Wait a bit for all content to be fully loaded
            time.sleep(2)

            # Common selectors for tables and data rows
            possible_selectors = [
                "table tbody tr",
                ".tracking-link",
                ".link-item",
                "[data-tracking]",
                ".table-row",
                "tr:has(td)"
            ]

            rows = None
            for selector in possible_selectors:
                try:
                    rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if rows:
                        print(f"Found {len(rows)} rows using selector: {selector}")
                        break
                except:
                    continue
            
            if not rows:
                # Fallback: look for any elements containing numbers that might be clicks/fans
                print("Trying fallback method to find data...")
                page_source = self.driver.page_source
                print("Page source length:", len(page_source))
                
                # Save page source for debugging
                with open("page_source.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                print("Page source saved to page_source.html for debugging")
                
                return tracking_data
            
            for row in rows:
                try:
                    # Extract text from the row
                    row_text = row.text.strip()
                    if not row_text:
                        continue
                    
                    # Look for patterns that might contain tracking link name, clicks, and fans
                    # This is a flexible approach that can be adjusted based on actual page structure
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if not cells:
                        cells = row.find_elements(By.CSS_SELECTOR, "div, span")
                    
                    if len(cells) >= 4:  # Need at least 4 columns: Name, Clicks, Fans, Earnings
                        # Assume first cell is name, and look for numbers in other cells
                        raw_name = cells[0].text.strip()

                        # Clean up the name - remove "Created XX-XX-XXXX" part
                        name = raw_name.split('\nCreated')[0].strip()
                        if not name:
                            name = raw_name.split('Created')[0].strip()

                        clicks = 0
                        fans = 0
                        earnings = 0.00

                        # Debug: Print all cell contents to understand table structure
                        print(f"DEBUG - Row cells ({len(cells)} total):")
                        for i, cell in enumerate(cells):
                            cell_text = cell.text.strip()
                            print(f"  Column {i}: '{cell_text}'")

                        # Extract data from cells - search for earnings in all columns
                        try:
                            # Extract clicks and fans from first numeric columns
                            numeric_values = []
                            for i, cell in enumerate(cells[1:], 1):  # Skip name column
                                cell_text = cell.text.strip()
                                # Skip percentage values and earnings (dollar signs)
                                if '%' not in cell_text and '$' not in cell_text:
                                    # Look for integer values (clicks, fans)
                                    numbers = re.findall(r'\d+', cell_text)
                                    if numbers:
                                        numeric_values.append(int(numbers[0]))

                            # Assign first two numeric values to clicks and fans
                            if len(numeric_values) >= 1:
                                clicks = numeric_values[0]
                            if len(numeric_values) >= 2:
                                fans = numeric_values[1]

                            # Extract earnings from the last column (rightmost column)
                            # Based on the screenshot, earnings appear in the rightmost column
                            earnings_found = False
                            for i in range(len(cells) - 1, 0, -1):  # Search from right to left, skip name column
                                earnings_text = cells[i].text.strip()
                                print(f"DEBUG - Column {i} text: '{earnings_text}'")

                                # Look for dollar amounts like $630.40, $283.11, $2,361.50, etc.
                                earnings_match = re.search(r'\$\s*[\d,]+(?:\.\d+)?', earnings_text)
                                if earnings_match:
                                    try:
                                        # Remove $ and commas and spaces, convert to float
                                        earnings_str = earnings_match.group().replace('$', '').replace(',', '').replace(' ', '')
                                        earnings = float(earnings_str)
                                        print(f"DEBUG - Found earnings ${earnings:.2f} in column {i}")
                                        earnings_found = True
                                        break
                                    except ValueError as e:
                                        print(f"DEBUG - Error parsing earnings: {e}")
                                        continue

                            if not earnings_found:
                                print(f"DEBUG - No earnings found in any column")
                        except (IndexError, ValueError) as e:
                            print(f"Error extracting data from row: {e}")
                            continue

                        if name and (clicks > 0 or fans > 0 or earnings > 0):
                            tracking_data.append((name, clicks, fans, earnings))
                            print(f"Extracted: {name} - Clicks: {clicks}, Fans: {fans}, Earnings: ${earnings:.2f}")
                
                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue
            
            print(f"Successfully extracted {len(tracking_data)} tracking links")

            # Debug: Print all extracted link names to verify we got everything
            print("\nExtracted tracking links:")
            for name, clicks, fans, earnings in tracking_data:
                print(f"  - {name}")

            # Check if we got all priority links from config
            from config import PRIORITY_LINKS
            extracted_names = [name for name, _, _, _ in tracking_data]
            missing_links = []

            for priority_link in PRIORITY_LINKS:
                # Check if priority link exists in extracted data (case-insensitive partial match)
                found = False
                for extracted_name in extracted_names:
                    if priority_link.lower() in extracted_name.lower() or extracted_name.lower() in priority_link.lower():
                        found = True
                        break
                if not found:
                    missing_links.append(priority_link)

            if missing_links:
                print(f"\n⚠️  WARNING: Missing {len(missing_links)} priority links:")
                for link in missing_links:
                    print(f"  - {link}")
                print("These links may need to be loaded with additional 'Load More' clicks or may have different names.")
            else:
                print("\n✅ All priority links found!")

            return tracking_data
            
        except Exception as e:
            print(f"Error extracting tracking data: {e}")
            return tracking_data
    
    def run_scraping(self):
        """Main method to run the complete scraping process"""
        try:
            print("Starting OnlyMonster scraping...")
            
            # Login
            self.login()
            
            # Navigate to tracking links
            self.navigate_to_tracking_links()
            
            # Extract data
            tracking_data = self.extract_tracking_data()
            
            if tracking_data:
                # Store in database
                self.db.insert_tracking_data(tracking_data)
                print(f"Successfully stored {len(tracking_data)} records in database")
            else:
                print("No tracking data found to store")
            
            return tracking_data
            
        except Exception as e:
            print(f"Scraping failed: {e}")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                print("Browser closed")


if __name__ == "__main__":
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        print("Scraping completed successfully!")

        # Run analytics after successful scraping
        if data:
            print("\n" + "="*60)
            print("🔍 RUNNING ANALYTICS...")
            print("="*60)

            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
            analytics.print_analysis_report(analysis)
        else:
            print("No data collected, skipping analysis.")

    except Exception as e:
        print(f"Scraping failed: {e}")
