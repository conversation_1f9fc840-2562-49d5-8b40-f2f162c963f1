#!/bin/bash

# OnlyMonster Weekly Report Automation Setup
echo "🔧 Setting up OnlyMonster Weekly Report Automation..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root (use sudo)"
    exit 1
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Working directory: $SCRIPT_DIR"

# Make scripts executable
echo "🔐 Making scripts executable..."
chmod +x "$SCRIPT_DIR/weekly_report.py"
chmod +x "$SCRIPT_DIR/weekly_scheduler.py"

# Copy systemd service files
echo "📋 Installing systemd service files..."
cp "$SCRIPT_DIR/onlymonster-weekly.service" /etc/systemd/system/
cp "$SCRIPT_DIR/onlymonster-weekly.timer" /etc/systemd/system/

# Reload systemd
echo "🔄 Reloading systemd daemon..."
systemctl daemon-reload

# Enable and start the timer
echo "⏰ Enabling weekly timer..."
systemctl enable onlymonster-weekly.timer
systemctl start onlymonster-weekly.timer

# Check status
echo "📊 Checking timer status..."
systemctl status onlymonster-weekly.timer --no-pager

echo ""
echo "✅ Weekly automation setup complete!"
echo ""
echo "📅 Weekly reports will run every Sunday at 9:00 AM"
echo ""
echo "🔧 Management commands:"
echo "  • Check timer status:    systemctl status onlymonster-weekly.timer"
echo "  • Check service status:  systemctl status onlymonster-weekly.service"
echo "  • View logs:            journalctl -u onlymonster-weekly.service -f"
echo "  • Run manually:         python3 $SCRIPT_DIR/weekly_report.py"
echo "  • Stop automation:      systemctl stop onlymonster-weekly.timer"
echo "  • Start automation:     systemctl start onlymonster-weekly.timer"
echo ""
echo "📋 Next scheduled run:"
systemctl list-timers onlymonster-weekly.timer --no-pager
