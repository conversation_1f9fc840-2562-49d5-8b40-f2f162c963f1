#!/usr/bin/env python3
"""
Test script for OnlyMonster Weekly Report functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from weekly_report import (
    get_weekly_date_range,
    get_weekly_historical_data,
    calculate_weekly_metrics,
    format_weekly_slack_message
)
from database import TrackingDatabase


def test_weekly_date_range():
    """Test weekly date range calculation"""
    print("🗓️  Testing weekly date range...")
    
    start_date, end_date, days_count = get_weekly_date_range()
    
    print(f"   Start Date: {start_date}")
    print(f"   End Date: {end_date}")
    print(f"   Days Count: {days_count}")
    
    assert days_count == 7, f"Expected 7 days, got {days_count}"
    print("   ✅ Date range calculation working correctly")


def test_weekly_data_retrieval():
    """Test weekly historical data retrieval"""
    print("\n📊 Testing weekly data retrieval...")
    
    try:
        data_by_link = get_weekly_historical_data()
        
        print(f"   Found data for {len(data_by_link)} tracking links")
        
        for link, entries in list(data_by_link.items())[:3]:  # Show first 3
            print(f"   📈 {link}: {len(entries)} data points")
            if entries:
                latest = entries[0]
                print(f"      Latest: {latest['fans']} fans, {latest['clicks']} clicks, ${latest['earnings']:.2f}")
        
        print("   ✅ Data retrieval working correctly")
        return data_by_link
        
    except Exception as e:
        print(f"   ❌ Data retrieval failed: {e}")
        return {}


def test_weekly_metrics_calculation():
    """Test weekly metrics calculation"""
    print("\n🧮 Testing weekly metrics calculation...")
    
    data_by_link = get_weekly_historical_data()
    
    if not data_by_link:
        print("   ⚠️  No data available for metrics calculation")
        return {}
    
    try:
        metrics = calculate_weekly_metrics(data_by_link)
        
        print(f"   Calculated metrics for {len(metrics)} links")
        
        # Show metrics for first few links
        for link, data in list(metrics.items())[:3]:
            print(f"   📊 {link}:")
            print(f"      Fan Growth: {data['fan_growth']:+d}")
            print(f"      Click Growth: {data['click_growth']:+,d}")
            print(f"      Earnings Growth: ${data['earnings_growth']:+.2f}")
            print(f"      Conversion Rate: {data['conversion_rate']:.2f}%")
            print(f"      Earnings per Fan: ${data['earnings_per_fan']:.2f}")
        
        print("   ✅ Metrics calculation working correctly")
        return metrics
        
    except Exception as e:
        print(f"   ❌ Metrics calculation failed: {e}")
        return {}


def test_slack_message_formatting():
    """Test Slack message formatting"""
    print("\n📱 Testing Slack message formatting...")
    
    # Create sample report data
    sample_metrics = {
        'reels-naominoface': {
            'fan_growth': 15,
            'click_growth': 250,
            'earnings_growth': 12.50,
            'current_earnings': 45.75,
            'current_fans': 120,
            'conversion_rate': 4.2
        },
        'reddit-babycheeksx': {
            'fan_growth': 8,
            'click_growth': 180,
            'earnings_growth': 8.25,
            'current_earnings': 32.10,
            'current_fans': 95,
            'conversion_rate': 3.8
        }
    }
    
    sample_report_data = {
        'metrics': sample_metrics,
        'total_new_fans': 23,
        'total_new_clicks': 430,
        'total_earnings_growth': 20.75,
        'period': '8/2/25 → 8/9/25',
        'days_count': 7
    }
    
    sample_ai_insights = "Focus on scaling reels-naominoface campaigns. Reddit platform shows strong engagement potential."
    
    try:
        message = format_weekly_slack_message(sample_report_data, sample_ai_insights)
        
        print("   📝 Generated Slack message:")
        print("   " + "="*50)
        for line in message.split('\n'):
            print(f"   {line}")
        print("   " + "="*50)
        
        # Basic validation
        assert "Weekly Report" in message
        assert "23" in message  # total fans
        assert "430" in message  # total clicks
        assert "$20.75" in message  # earnings growth
        
        print("   ✅ Slack message formatting working correctly")
        
    except Exception as e:
        print(f"   ❌ Slack message formatting failed: {e}")


def test_database_connection():
    """Test database connection and recent data"""
    print("\n🗄️  Testing database connection...")
    
    try:
        db = TrackingDatabase()
        recent_data = db.get_latest_data(5)
        
        print(f"   Found {len(recent_data)} recent records")
        
        if recent_data:
            print("   📊 Recent data sample:")
            for name, clicks, fans, earnings, timestamp in recent_data[:3]:
                print(f"      {name}: {fans} fans, {clicks} clicks, ${earnings:.2f} ({timestamp})")
        
        print("   ✅ Database connection working correctly")
        
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")


def main():
    """Run all weekly report tests"""
    print("🧪 ONLYMONSTER WEEKLY REPORT TESTS")
    print("="*50)
    
    # Run tests
    test_database_connection()
    test_weekly_date_range()
    test_weekly_data_retrieval()
    test_weekly_metrics_calculation()
    test_slack_message_formatting()
    
    print("\n" + "="*50)
    print("✅ All weekly report tests completed!")
    print("\n💡 To run the actual weekly report:")
    print("   python3 weekly_report.py")
    print("\n📅 To set up weekly automation:")
    print("   sudo bash setup_weekly_automation.sh")


if __name__ == "__main__":
    main()
