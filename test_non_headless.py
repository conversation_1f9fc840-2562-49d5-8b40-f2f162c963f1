#!/usr/bin/env python3
"""
Test script to try non-headless mode to see if Clerk.js loads properly
"""
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
from config import EMAIL, PASSWORD, LOGIN_URL

def test_non_headless_login():
    """Test login without headless mode"""
    chrome_options = Options()
    
    # Don't use headless mode for testing
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Additional options to help with JavaScript execution
    chrome_options.add_argument("--enable-javascript")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        # Try to use system chromedriver first
        service = Service("/usr/bin/chromedriver")
        driver = webdriver.Chrome(service=service, options=chrome_options)
    except Exception as e1:
        print(f"System chromedriver failed: {e1}")
        return False
    
    try:
        print("Navigating to OnlyMonster.ai sign-in page...")
        driver.get(LOGIN_URL)
        
        # Wait for page to load
        time.sleep(5)
        
        # Check if Clerk.js loads
        print("Checking if Clerk.js loads...")
        clerk_loaded = driver.execute_script("return window.Clerk !== undefined")
        print(f"Clerk.js loaded: {clerk_loaded}")
        
        if not clerk_loaded:
            print("Waiting for Clerk.js to load...")
            for i in range(30):
                time.sleep(1)
                clerk_loaded = driver.execute_script("return window.Clerk !== undefined")
                if clerk_loaded:
                    print(f"Clerk.js loaded after {i+1} seconds")
                    break
                print(f"Attempt {i+1}/30: Still waiting for Clerk.js...")
        
        # Check sign-in block content
        sign_in_content = driver.execute_script("return document.getElementById('sign-in-block').innerHTML")
        print(f"Sign-in block content length: {len(sign_in_content)}")
        print(f"Sign-in block content preview: {sign_in_content[:200]}...")
        
        # Look for input fields
        inputs = driver.find_elements(By.CSS_SELECTOR, "#sign-in-block input")
        print(f"Found {len(inputs)} input fields in sign-in block")
        
        for i, input_elem in enumerate(inputs):
            try:
                input_type = input_elem.get_attribute("type")
                input_name = input_elem.get_attribute("name")
                input_placeholder = input_elem.get_attribute("placeholder")
                print(f"Input {i+1}: type={input_type}, name={input_name}, placeholder={input_placeholder}")
            except:
                print(f"Input {i+1}: Could not get attributes")
        
        # Save page source for analysis
        with open("non_headless_debug.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Page source saved to non_headless_debug.html")
        
        # Take a screenshot
        driver.save_screenshot("non_headless_screenshot.png")
        print("Screenshot saved to non_headless_screenshot.png")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    test_non_headless_login()
