# OnlyMonster Weekly Report System

## Overview

The OnlyMonster Weekly Report provides comprehensive weekly analytics for tracking link performance, including subscriber growth, revenue analysis, and AI-powered strategic insights. This complements the daily reports with longer-term trend analysis and strategic recommendations.

## Features

### 📊 Weekly Metrics Analysis
- **Subscriber Growth**: Weekly fan acquisition by platform
- **Revenue Performance**: Weekly earnings changes and totals
- **Conversion Rate Analysis**: Fan conversion efficiency metrics
- **Platform Comparison**: Performance ranking across all tracking links

### 🤖 AI-Powered Insights
- Strategic growth opportunities identification
- Revenue optimization recommendations
- Platform-specific improvement suggestions
- Conversion rate enhancement strategies
- Weekly trend analysis and predictions

### 📱 Slack Integration
- Automated weekly report delivery
- Formatted messages with key metrics
- Error notifications for failed reports
- Startup/shutdown notifications

## Report Sections

### 1. Weekly Subscriber Growth
Shows platforms that gained new subscribers during the week:
```
👥 WEEKLY SUBSCRIBER GROWTH:
🚀 Reels2024: +20 fans (+902 clicks)
⬆️ reddit-babycheeksx-combined: +3 fans (+93 clicks)
```

### 2. Weekly Revenue Performance
Displays revenue changes and earnings per fan:
```
💰 WEEKLY REVENUE PERFORMANCE:
📉 reels-naominoface: $-2017.00 (Total: $3388.00)
   └─ $20.29 per fan (167 fans)
```

### 3. Top Performing Platforms
Highlights best performers across key metrics:
```
🏆 TOP PERFORMING PLATFORMS:
📈 Subscriber Growth Leaders:
   1. Reels2024: +20 fans
💰 Revenue Leaders:
   1. reels-naominoface: $3388.00
```

### 4. Conversion Rate Analysis
Shows fan conversion efficiency:
```
📊 CONVERSION RATE ANALYSIS:
🎯 Clapper: 20.00% conversion
   └─ 1 fans from 5 clicks
```

### 5. AI Strategic Insights
Comprehensive AI analysis with actionable recommendations:
- Top 3 strategic opportunities for growth
- Revenue optimization recommendations
- Platform-specific improvement suggestions
- Conversion rate enhancement strategies
- Weekly trend analysis and predictions

## Usage

### Manual Execution
```bash
# Generate weekly report
python weekly_report.py

# Test weekly report functionality
python tests/test_weekly_report.py
```

### Automated Scheduling
```bash
# Set up weekly automation (run as root)
sudo bash setup_weekly_automation.sh

# Check automation status
systemctl status onlymonster-weekly.timer
systemctl status onlymonster-weekly.service

# View automation logs
journalctl -u onlymonster-weekly.service -f
```

### Manual Scheduler
```bash
# Run continuous weekly scheduler
python weekly_scheduler.py
```

## Configuration

The weekly report uses the same configuration as daily reports:

- **Database**: `DATABASE_PATH` from `config.py`
- **Priority Links**: `PRIORITY_LINKS` from `config.py`
- **Slack Integration**: `SLACK_BOT_TOKEN` and `SLACK_CHANNEL_ID`
- **AI Analysis**: OpenRouter API via `TrackingAnalytics`

## Automation Schedule

- **Frequency**: Every Sunday at 9:00 AM
- **Service**: `onlymonster-weekly.service`
- **Timer**: `onlymonster-weekly.timer`
- **Logs**: Available via `journalctl -u onlymonster-weekly.service`

## Data Requirements

The weekly report analyzes data from the past 7 days and requires:

- At least 2 data points per tracking link for growth calculations
- Historical data with timestamps for trend analysis
- Earnings data for revenue performance metrics
- Combined link data via `LinkCombiner` for accurate metrics

## Key Metrics

### Growth Metrics
- **Fan Growth**: New subscribers gained during the week
- **Click Growth**: New clicks generated during the week
- **Earnings Growth**: Revenue change from previous week

### Performance Indicators
- **Conversion Rate**: Fans/Clicks ratio as percentage
- **Earnings per Fan**: Revenue efficiency per subscriber
- **Platform Ranking**: Performance comparison across links

### AI Analysis Areas
- **Strategic Opportunities**: Growth potential identification
- **Revenue Optimization**: Earnings improvement strategies
- **Platform Insights**: Channel-specific recommendations
- **Trend Predictions**: Future performance forecasting

## Error Handling

The weekly report includes comprehensive error handling:

- **Database Connection**: Graceful handling of connection issues
- **Data Validation**: Checks for sufficient historical data
- **Slack Integration**: Error notifications for failed deliveries
- **AI Analysis**: Fallback messaging when AI is unavailable

## Files Structure

```
weekly_report.py              # Main weekly report generator
weekly_scheduler.py           # Continuous scheduler for weekly reports
onlymonster-weekly.service    # SystemD service file
onlymonster-weekly.timer      # SystemD timer file
setup_weekly_automation.sh    # Automation setup script
tests/test_weekly_report.py   # Test suite for weekly functionality
```

## Integration with Daily Reports

The weekly report complements daily reports by providing:

- **Longer-term trends** vs daily snapshots
- **Strategic insights** vs operational updates
- **Platform comparison** vs individual performance
- **Growth planning** vs immediate actions

Both reports share the same data source and configuration, ensuring consistency across reporting timeframes.

## Troubleshooting

### Common Issues

1. **Insufficient Data**: Ensure at least 7 days of historical data
2. **Missing Earnings**: Check database schema includes earnings column
3. **Slack Delivery**: Verify bot permissions and channel access
4. **AI Analysis**: Confirm OpenRouter API key configuration

### Debug Commands

```bash
# Test database connection
python -c "from database import TrackingDatabase; db = TrackingDatabase(); print(db.get_latest_data(5))"

# Test weekly data retrieval
python -c "from weekly_report import get_weekly_historical_data; print(len(get_weekly_historical_data()))"

# Test Slack formatting
python tests/test_weekly_report.py
```

## Future Enhancements

Potential improvements for the weekly report system:

- **Monthly Reports**: Extended timeframe analysis
- **Comparative Analysis**: Week-over-week comparisons
- **Visual Charts**: Graph generation for trends
- **Custom Timeframes**: Flexible date range selection
- **Export Options**: CSV/PDF report generation
